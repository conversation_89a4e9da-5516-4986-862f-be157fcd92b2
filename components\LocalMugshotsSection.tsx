"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import UniversalNavLink from "@/components/UniversalNavLink"
import MugshotModal from "@/components/MugshotModal"
import MugshotCard from "@/components/MugshotCard"
import LocationDropdown from "@/components/LocationDropdown"
import { useAuthStore } from "@/lib/stores/auth-store"
import { useLocationForUnauthenticatedUsers } from "@/lib/hooks/queries/use-detected-location"
import { useMugshotsQuery } from "@/lib/hooks/queries/use-mugshots-query"
import { transformDBMugshotToUI, type UIMugshot } from "@/lib/utils/mugshot-transforms"
import { MapPin, Users, TrendingUp, Loader2 } from "lucide-react"

// Use the standard UIMugshot type from transforms
type SelectedMugshot = UIMugshot

export default function LocalMugshotsSection() {
  const [userLocation, setUserLocation] = useState<string>("Your Location")
  const [selectedState, setSelectedState] = useState<string>("")
  const [selectedCounty, setSelectedCounty] = useState<string>("")
  const [isLocationDetected, setIsLocationDetected] = useState(false)
  const [selectedMugshot, setSelectedMugshot] = useState<SelectedMugshot | null>(null)
  const [isMugshotModalOpen, setIsMugshotModalOpen] = useState(false)

  // Get user's home location from auth store
  const { getHomeLocation, hasHomeLocation, loadUserProfile, isAuthenticated, isLoading: authLoading } = useAuthStore()

  // Get detected location for unauthenticated users
  const {
    data: detectedLocation,
    isLoading: locationDetecting,
    error: locationError
  } = useLocationForUnauthenticatedUsers({
    fallbackState: 'California',
    staleTime: 30 * 60 * 1000 // 30 minutes
  })

  // Initialize location based on authentication state
  useEffect(() => {
    // For authenticated users: use home location if available
    if (isAuthenticated && !authLoading && hasHomeLocation()) {
      const { state, county } = getHomeLocation()
      if (state && county) {
        setSelectedState(state)
        setSelectedCounty(county)
        setUserLocation(`${county}, ${state}`)
        setIsLocationDetected(true)
        return
      }
    }

    // For authenticated users without location: try loading profile
    if (isAuthenticated && !authLoading && !hasHomeLocation()) {
      const loadLocationOnce = async () => {
        await loadUserProfile()

        if (hasHomeLocation()) {
          const { state, county } = getHomeLocation()
          if (state && county) {
            setSelectedState(state)
            setSelectedCounty(county)
            setUserLocation(`${county}, ${state}`)
            setIsLocationDetected(true)
            return
          }
        }
      }
      loadLocationOnce()
      return
    }

    // For unauthenticated users: use detected location
    if (!isAuthenticated && !authLoading && !locationDetecting && detectedLocation) {
      console.log('🎯 Using detected location for local mugshots:', detectedLocation.state)
      setSelectedState(detectedLocation.state)
      setSelectedCounty("") // We only detect state, not county
      setUserLocation(detectedLocation.state)
      setIsLocationDetected(true)
    }
  }, [
    isAuthenticated,
    authLoading,
    hasHomeLocation,
    getHomeLocation,
    loadUserProfile,
    locationDetecting,
    detectedLocation
  ])

  // Determine the effective state for data fetching
  // Priority: 1) Selected state, 2) Detected state for unauthenticated users, 3) Home state for authenticated users
  const getEffectiveState = () => {
    // If user has manually selected a state, use that
    if (selectedState && selectedState !== 'all-states') {
      return selectedState
    }

    // For unauthenticated users, use detected location
    if (!isAuthenticated && detectedLocation?.state) {
      // TEMPORARY: Test with Florida instead of Ohio to check data quality
      return 'Florida' // detectedLocation.state
    }

    // For authenticated users, use home location
    if (isAuthenticated && hasHomeLocation()) {
      const { state } = getHomeLocation()
      return state
    }

    return undefined
  }

  const effectiveState = getEffectiveState()
  const effectiveCounty = selectedCounty && selectedCounty !== 'all-counties' ? selectedCounty : undefined

  console.log('🎯 LocalMugshotsSection effective state:', {
    selectedState,
    detectedState: detectedLocation?.state,
    effectiveState,
    isAuthenticated,
    testingWithFlorida: true
  })

  // Fetch real mugshots data based on detected/selected location
  const {
    data: mugshotsData,
    isLoading: mugshotsLoading,
    error: mugshotsError
  } = useMugshotsQuery(
    {
      state: effectiveState,
      county: effectiveCounty
    },
    { sortBy: 'newest' },
    { page: 1, perPage: 8, includeTotal: false }
  )

  // Debug: Log the exact structure of raw API data
  if (mugshotsData?.mugshots && mugshotsData.mugshots.length > 0) {
    const firstRawMugshot = mugshotsData.mugshots[0]
    console.log('🔍 Raw API mugshot structure:', {
      id: firstRawMugshot.id,
      firstName: firstRawMugshot.firstName,
      lastName: firstRawMugshot.lastName,
      stateOfBooking: firstRawMugshot.stateOfBooking,
      countyOfBooking: firstRawMugshot.countyOfBooking,
      imagePath: firstRawMugshot.imagePath,
      hasName: 'name' in firstRawMugshot,
      hasLocation: 'location' in firstRawMugshot,
      allKeys: Object.keys(firstRawMugshot)
    })
  }

  // Transform database mugshots to UI format (same as mugshots page)
  const localMugshots = mugshotsData?.mugshots ?
    mugshotsData.mugshots.map(transformDBMugshotToUI) : []

  console.log('🏠 LocalMugshotsSection data:', {
    effectiveState,
    effectiveCounty,
    mugshotsCount: localMugshots.length,
    isLoading: mugshotsLoading,
    hasError: !!mugshotsError,
    isLocationDetected,
    selectedState,
    detectedLocation: detectedLocation?.state,
    mugshotsData: mugshotsData ? 'present' : 'null',
    rawMugshotsCount: mugshotsData?.mugshots?.length || 0,
    firstMugshot: localMugshots[0] ? {
      id: localMugshots[0].id,
      name: localMugshots[0].name,
      location: localMugshots[0].location,
      image: localMugshots[0].image
    } : 'none'
  })

  const handleCardClick = (mugshot: SelectedMugshot) => {
    setSelectedMugshot(mugshot)
    setIsMugshotModalOpen(true)
  }

  // Update location display when state or county changes
  useEffect(() => {
    if (selectedState && selectedCounty) {
      setUserLocation(`${selectedCounty}, ${selectedState}`)
    } else if (selectedState) {
      setUserLocation(`${selectedState}`)
    }
  }, [selectedState, selectedCounty])

  // Determine loading and error states
  const isLoading = authLoading || locationDetecting || mugshotsLoading
  const hasError = locationError || mugshotsError
  const showMugshots = localMugshots.length > 0 && !isLoading
  const hasLocationButNoMugshots = isLocationDetected && !isLoading && localMugshots.length === 0

  console.log('🎭 Render conditions:', {
    isLoading,
    hasError,
    showMugshots,
    hasLocationButNoMugshots,
    localMugshotsLength: localMugshots.length
  })

  return (
    <div className="w-full">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold uppercase tracking-tight text-white mb-4">
          <MapPin className="inline h-8 w-8 text-cyan-500 mr-2" />
          <span className="text-cyan-500">LOCAL</span> DISCOVERIES
        </h2>
        <p className="text-xl text-gray-400 max-w-2xl mx-auto mb-6">
          {isLoading && (
            <span className="flex items-center justify-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Discovering mugshots in your area...
            </span>
          )}
          {!isLoading && isLocationDetected && (
            <>
              Hottest mugshots near {userLocation} - vote on your neighbors!
              {detectedLocation && !isAuthenticated && (
                <span className="block text-sm text-cyan-400 mt-1">
                  📍 Auto-detected from your location ({Math.round(detectedLocation.distance || 0)} miles away)
                </span>
              )}
            </>
          )}
          {!isLoading && !isLocationDetected && hasError && (
            <span className="text-red-400">
              Unable to detect your location. Please select manually.
            </span>
          )}
        </p>

        {/* Location Selector - Using our new LocationDropdown component */}
        <div className="flex justify-center w-full mb-8">
          <div className="w-full max-w-md">
            <LocationDropdown
              selectedState={selectedState}
              setSelectedState={setSelectedState}
              selectedCounty={selectedCounty}
              setSelectedCounty={setSelectedCounty}
              statePlaceholder="Select your state"
              countyPlaceholder="Select county"
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Stats */}
      {isLocationDetected && !isLoading && (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          <div className="card-neon p-4 text-center">
            <MapPin className="h-6 w-6 text-cyan-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">{localMugshots.length}</div>
            <div className="text-sm text-gray-400">Recent Mugshots</div>
          </div>
          <div className="card-neon p-4 text-center">
            <Users className="h-6 w-6 text-pink-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">
              {localMugshots.reduce((total, mugshot) => total + (mugshot.votes || 0), 0)}
            </div>
            <div className="text-sm text-gray-400">Total Votes</div>
          </div>
          <div className="card-neon p-4 text-center">
            <TrendingUp className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-bold text-white">
              {detectedLocation?.totalMugshots ?
                detectedLocation.totalMugshots.toLocaleString() :
                localMugshots.length > 0 ? 'Active' : 'N/A'
              }
            </div>
            <div className="text-sm text-gray-400">
              {detectedLocation?.totalMugshots ? 'Total in State' : 'Status'}
            </div>
          </div>
        </div>
      )}

      {/* Local Mugshots Grid */}
      {isLoading ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="card-neon p-4 animate-pulse">
              <div className="bg-gray-700 rounded-lg h-48 mb-3"></div>
              <div className="bg-gray-700 rounded h-4 mb-2"></div>
              <div className="bg-gray-700 rounded h-3 w-2/3"></div>
            </div>
          ))}
        </div>
      ) : showMugshots ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
          {localMugshots.map((mugshot) => (
            <MugshotCard
              key={mugshot.id}
              mugshot={mugshot}
              onClick={handleCardClick}
              cardSize="small"
              showLocalBadge={false}
              ratingStats={mugshot.averageRating !== undefined ? {
                averageRating: mugshot.averageRating || 0,
                totalRatings: mugshot.totalRatings || 0
              } : undefined}
            />
          ))}
        </div>
      ) : hasLocationButNoMugshots ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <MapPin className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="text-lg">No recent mugshots found in {userLocation}</p>
            <p className="text-sm">Check back later for new additions or try browsing all mugshots.</p>
            <div className="mt-4">
              <UniversalNavLink
                href="/mugshots"
                className="inline-flex items-center gap-2 text-cyan-400 hover:text-cyan-300 transition-colors"
              >
                Browse All Mugshots →
              </UniversalNavLink>
            </div>
          </div>
        </div>
      ) : hasError && isLocationDetected ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <p className="text-lg text-red-400">Error loading mugshots</p>
            <p className="text-sm">Please try again later or browse all mugshots.</p>
            <div className="mt-4">
              <UniversalNavLink
                href="/mugshots"
                className="inline-flex items-center gap-2 text-cyan-400 hover:text-cyan-300 transition-colors"
              >
                Browse All Mugshots →
              </UniversalNavLink>
            </div>
          </div>
        </div>
      ) : null}

      {/* Call to Action */}
      <div className="text-center">
        <UniversalNavLink
          href={effectiveState ? `/mugshots?state=${encodeURIComponent(effectiveState)}` : '/mugshots'}
        >
          <Button className="btn-glow-cyan cursor-pointer">
            View All {effectiveState ? `${effectiveState} ` : 'Local '}Mugshots
          </Button>
        </UniversalNavLink>
      </div>

      {/* Mugshot Modal */}
      {selectedMugshot && (
        <MugshotModal
          mugshot={selectedMugshot}
          isOpen={isMugshotModalOpen}
          onClose={() => setIsMugshotModalOpen(false)}
        />
      )}
    </div>
  )
} 