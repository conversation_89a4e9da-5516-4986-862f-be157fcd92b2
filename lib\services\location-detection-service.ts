/**
 * Location Detection Service
 *
 * Provides IP-based location detection for unauthenticated users using FreeIPAPI.
 * Implements session-based caching with localStorage to minimize API calls.
 *
 * Key Features:
 * - Single API call per browser session
 * - Persistent localStorage caching with consistent key naming
 * - Comprehensive error handling with fallback states
 * - TypeScript interfaces for type safety
 */

import { handleLocationError } from '@/lib/utils/location-error-handling'

// Cache key for localStorage - consistent with atm_ prefix
const LOCATION_CACHE_KEY = 'atm_user_location_cache'
const NEAREST_STATE_CACHE_KEY = 'atm_nearest_state_cache'
const CACHE_EXPIRY_HOURS = 24 // Cache expires after 24 hours

// IP API response interface (ipapi.co format)
export interface IPAPIResponse {
  ip: string
  city: string
  region: string
  region_code: string
  country: string
  country_code: string
  country_name: string
  postal: string
  latitude: number
  longitude: number
  timezone: string
  utc_offset: string
  country_calling_code: string
  currency: string
  languages: string
  asn: string
  org: string
}

// Cached location data interface
export interface CachedLocationData {
  coordinates: {
    latitude: number
    longitude: number
  }
  location: {
    city: string
    region: string
    country: string
    countryCode: string
  }
  timestamp: number
  source: 'ipapi'
}

// Service response interface
export interface LocationDetectionResult {
  coordinates: {
    latitude: number
    longitude: number
  }
  location: {
    city: string
    region: string
    country: string
    countryCode: string
  }
  cached: boolean
  error?: string
}

/**
 * Location Detection Service Class
 */
export class LocationDetectionService {
  private static instance: LocationDetectionService
  private readonly apiUrl = 'https://ipapi.co/json/' // CORS-friendly alternative
  private readonly timeoutMs = 10000 // 10 second timeout
  private readonly fallbackState = 'California' // Default fallback state

  private constructor() {}

  static getInstance(): LocationDetectionService {
    if (!LocationDetectionService.instance) {
      LocationDetectionService.instance = new LocationDetectionService()
    }
    return LocationDetectionService.instance
  }

  /**
   * Get user location via IP detection with caching
   */
  async detectUserLocation(): Promise<LocationDetectionResult> {
    try {
      // Check cache first
      const cachedData = this.getCachedLocation()
      if (cachedData) {
        return {
          ...cachedData,
          cached: true
        }
      }

      // Make API call if no valid cache
      const apiResponse = await this.callFreeIPAPI()
      
      // Cache the response
      const locationData: CachedLocationData = {
        coordinates: {
          latitude: apiResponse.latitude,
          longitude: apiResponse.longitude
        },
        location: {
          city: apiResponse.city,
          region: apiResponse.region,
          country: apiResponse.country_name,
          countryCode: apiResponse.country_code
        },
        timestamp: Date.now(),
        source: 'ipapi'
      }

      this.cacheLocation(locationData)

      return {
        ...locationData,
        cached: false
      }

    } catch (error) {
      // Use comprehensive error handling
      const { errorInfo, fallbackCoordinates } = handleLocationError(
        error,
        'location-detection-service'
      )

      // Return fallback location with error details
      return {
        coordinates: fallbackCoordinates,
        location: {
          city: 'Unknown',
          region: this.fallbackState,
          country: 'United States',
          countryCode: 'US'
        },
        cached: false,
        error: errorInfo.userMessage
      }
    }
  }

  /**
   * Call IP API with timeout and error handling
   */
  private async callFreeIPAPI(): Promise<IPAPIResponse> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeoutMs)

    try {
      const response = await fetch(this.apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'ATM-WebApp/1.0'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`IP API HTTP error: ${response.status}`)
      }

      const data: IPAPIResponse = await response.json()

      // Validate required fields
      if (typeof data.latitude !== 'number' || typeof data.longitude !== 'number') {
        throw new Error('Invalid coordinates in API response')
      }

      return data

    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Location detection timeout')
      }
      
      throw error
    }
  }

  /**
   * Get cached location data if valid
   */
  private getCachedLocation(): CachedLocationData | null {
    try {
      if (typeof window === 'undefined') return null // SSR safety

      const cached = localStorage.getItem(LOCATION_CACHE_KEY)
      if (!cached) return null

      const data: CachedLocationData = JSON.parse(cached)
      
      // Check if cache is expired
      const now = Date.now()
      const cacheAge = now - data.timestamp
      const maxAge = CACHE_EXPIRY_HOURS * 60 * 60 * 1000

      if (cacheAge > maxAge) {
        localStorage.removeItem(LOCATION_CACHE_KEY)
        return null
      }

      // Validate cached data structure
      if (!data.coordinates || !data.location || !data.timestamp) {
        localStorage.removeItem(LOCATION_CACHE_KEY)
        return null
      }

      return data

    } catch (error) {
      console.error('Error reading location cache:', error)
      // Clear corrupted cache
      try {
        localStorage.removeItem(LOCATION_CACHE_KEY)
      } catch {}
      return null
    }
  }

  /**
   * Cache location data in localStorage
   */
  private cacheLocation(data: CachedLocationData): void {
    try {
      if (typeof window === 'undefined') return // SSR safety

      localStorage.setItem(LOCATION_CACHE_KEY, JSON.stringify(data))
    } catch (error) {
      console.error('Error caching location data:', error)
      // Non-critical error, continue without caching
    }
  }

  /**
   * Clear cached location data (useful for testing or user preference changes)
   */
  clearCache(): void {
    try {
      if (typeof window === 'undefined') return

      localStorage.removeItem(LOCATION_CACHE_KEY)
    } catch (error) {
      console.error('Error clearing location cache:', error)
    }
  }

  /**
   * Check if location is cached and valid
   */
  isCached(): boolean {
    return this.getCachedLocation() !== null
  }
}

// Export singleton instance
export const locationDetectionService = LocationDetectionService.getInstance()
