import { createClient } from '@/lib/supabase/server'
import type { TagType } from '@/lib/constants'

// EXACT same interfaces as mugshots-service-server.ts (CRITICAL - DO NOT CHANGE)
export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  category?: string[] // Legacy field, will be deprecated
  tags?: TagType[] // Updated to use proper TagType from database
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

// EXACT same interface as DatabaseMugshot (CRITICAL - DO NOT CHANGE)
export interface DatabaseMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
  // Rating statistics
  average_rating?: number
  total_ratings?: number
  // Tag counts
  wild_count?: number
  funny_count?: number
  spooky_count?: number
  // User-specific data (when user is authenticated)
  user_rating?: number | null
  user_tags?: string[] // Array of tag types user has applied
}

/**
 * Native Query Implementation of Mugshots Service
 * 
 * This service replaces Supabase function calls with native TypeScript queries
 * while maintaining EXACT same API contracts and behavior as mugshots-service-server.ts
 * 
 * Performance benefits:
 * - Direct query control and optimization
 * - No function timeout issues (5 second limit)
 * - Easier debugging and maintenance
 * - Improved error handling
 */
class MugshotsNativeService {
  /**
   * Health check to verify database connectivity
   * PRESERVES: Exact same behavior as original service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const supabase = await createClient()
      const { error } = await supabase.from('mugshots').select('id').limit(1)
      return !error
    } catch {
      return false
    }
  }

  /**
   * Main mugshots fetching method - replaces function calls with native queries
   * PRESERVES: Exact same method signature and return type
   * 
   * @param filters - Filter parameters (search, location, dates, tags)
   * @param sortOptions - Sorting preferences (newest, top-rated, most-viewed)
   * @param pagination - Page and perPage settings
   * @param userId - Optional user ID for personalized data
   * @returns Promise<DatabaseMugshot[]> - Enhanced mugshots with rating/tag data
   */
  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      console.log('🚀 [Native Service] Fetching mugshots with filters:', { filters, sortOptions, pagination })
      
      // Step-by-step approach to replace complex Supabase function
      return await this.getFilteredMugshots(filters, sortOptions, pagination, userId)

    } catch (error) {
      console.error('❌ [Native Service] Error fetching mugshots:', error)
      // Return empty array to maintain same behavior as original service
      return []
    }
  }

  /**
   * Core native query implementation - replaces search_filtered_mugshots function
   * 
   * This method implements the same filtering, sorting, and aggregation logic
   * that was previously handled by the Supabase function.
   */
  private async getFilteredMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 },
    userId?: string
  ): Promise<DatabaseMugshot[]> {
    try {
      const supabase = await createClient()
             console.log('📋 [Native Service] Using efficient 6-step approach with filters:', filters)
       console.log('📋 [Native Service] Sort options:', sortOptions)

      // Calculate pagination offset
      const offset = (pagination.page - 1) * pagination.perPage
      let initialIds: number[] | null = null

      // STEP 1: Handle top-rated sorting
      if (sortOptions.sortBy === 'top-rated') {
        console.log('⭐ [Native Service] Step 1: Getting top-rated mugshot IDs')
        
        const { data: rated, error: ratedError } = await supabase
          .from('ratings')
          .select('mugshot_id, rating')
          
        if (ratedError) {
          console.error('❌ [Native Service] Failed to fetch ratings for top-rated:', ratedError)
          return []
        }

        if (rated && rated.length > 0) {
          // Calculate averages and get top 1000
          const ratingMap: Record<number, { sum: number; count: number }> = {}
          
          rated.forEach(r => {
            if (!ratingMap[r.mugshot_id]) {
              ratingMap[r.mugshot_id] = { sum: 0, count: 0 }
            }
            ratingMap[r.mugshot_id].sum += r.rating
            ratingMap[r.mugshot_id].count += 1
          })

          // Calculate averages and sort
          const averages = Object.keys(ratingMap).map(mugshotIdStr => {
            const mugshotId = parseInt(mugshotIdStr)
            const { sum, count } = ratingMap[mugshotId]
            return {
              mugshot_id: mugshotId,
              avg: sum / count
            }
          })

          // Sort by average rating descending and take top 1000
          averages.sort((a, b) => b.avg - a.avg)
          initialIds = averages.slice(0, 1000).map(r => r.mugshot_id)
          
          console.log(`⭐ [Native Service] Found ${initialIds.length} top-rated mugshots`)
        } else {
          initialIds = []
        }
      }

      // STEP 2: Apply tag filters
      if (filters.tags && filters.tags.length > 0) {
        console.log('🏷️ [Native Service] Step 2: Applying tag filters for tags:', filters.tags)
        
        const { data: tagged, error: tagError } = await supabase
          .from('tags')
          .select('mugshot_id')
          .in('tag_type', filters.tags)

        if (tagError) {
          console.error('❌ [Native Service] Tag filtering error:', tagError)
          return []
        }

        const tagFilteredIds = tagged?.map(t => t.mugshot_id) || []
        
        // Filter initialIds if we have them, otherwise use tagFilteredIds
        initialIds = initialIds
          ? initialIds.filter(id => tagFilteredIds.includes(id))
          : tagFilteredIds
          
        console.log(`🏷️ [Native Service] After tag filtering: ${initialIds.length} mugshots`)
      }

      // STEP 3: Build mugshot query
      console.log('📋 [Native Service] Step 3: Building mugshot query')
      
      let query = supabase
        .from('mugshots')
        .select(`
          id,
          created_at,
          firstName,
          lastName,
          dateOfBooking,
          stateOfBooking,
          countyOfBooking,
          offenseDescription,
          additionalDetails,
          imagePath,
          fb_status,
          adsText,
          jb_post_link,
          jb_fb_post
        `)
        .order('dateOfBooking', { ascending: false })
        .order('created_at', { ascending: false })

      // Apply initial IDs filter if we have them
      if (initialIds?.length) {
        query = query.in('id', initialIds)
      }

      // Apply other filters
      if (filters.searchTerm) {
        query = query.or(`firstName.ilike.%${filters.searchTerm}%,lastName.ilike.%${filters.searchTerm}%`)
      }

      if (filters.state && filters.state !== 'all-states') {
        query = query.eq('stateOfBooking', filters.state)
      }

      if (filters.county && filters.county !== 'all-counties') {
        query = query.eq('countyOfBooking', filters.county)
      }

      if (filters.dateFrom) {
        query = query.gte('dateOfBooking', filters.dateFrom)
      }

      if (filters.dateTo) {
        query = query.lte('dateOfBooking', filters.dateTo)
      }

      // STEP 4: Get mugshots for this page
      console.log('📄 [Native Service] Step 4: Applying pagination and executing query')
      
      const { data: mugshots, error: mugshotsError } = await query.range(offset, offset + pagination.perPage - 1)
      
      if (mugshotsError) {
        console.error('❌ [Native Service] Mugshots query failed:', mugshotsError)
        return []
      }

      if (!mugshots?.length) {
        console.log('📭 [Native Service] No mugshots found')
        return []
      }

      console.log(`✅ [Native Service] Found ${mugshots.length} mugshots for page ${pagination.page}`)

      // Get mugshot IDs for enrichment
      const ids = mugshots.map(m => m.id)

      // STEP 5: Enrich with rating & tag data
      console.log('💎 [Native Service] Step 5: Enriching with rating and tag data')
      
      const [ratings, tagList, userRatings, userTags] = await Promise.all([
        supabase.from('ratings').select('mugshot_id, rating').in('mugshot_id', ids),
        supabase.from('tags').select('mugshot_id, tag_type').in('mugshot_id', ids),
        userId ? supabase.from('ratings').select('mugshot_id, rating').in('mugshot_id', ids).eq('user_id', userId) : Promise.resolve({ data: [], error: null }),
        userId ? supabase.from('tags').select('mugshot_id, tag_type').in('mugshot_id', ids).eq('user_id', userId) : Promise.resolve({ data: [], error: null })
      ])

      // Create rating map
      const ratingMap = Object.fromEntries(
        ids.map(id => {
          const rates = ratings.data?.filter(r => r.mugshot_id === id) || []
          const avg = rates.reduce((sum, r) => sum + r.rating, 0) / (rates.length || 1)
          return [id, { average_rating: +avg.toFixed(2), total_ratings: rates.length }]
        })
      )

      // Create tag map
      const tagMap = Object.fromEntries(
        ids.map(id => {
          const tags = tagList.data?.filter(t => t.mugshot_id === id) || []
          const grouped = tags.reduce((acc, t) => {
            acc[t.tag_type] = (acc[t.tag_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
          return [id, grouped]
        })
      )

      // Create user rating map
      const userRatingMap: Record<number, number> = {}
      userRatings.data?.forEach(rating => {
        userRatingMap[rating.mugshot_id] = rating.rating
      })

      // Create user tag map
      const userTagMap: Record<number, string[]> = {}
      userTags.data?.forEach(tag => {
        if (!userTagMap[tag.mugshot_id]) {
          userTagMap[tag.mugshot_id] = []
        }
        userTagMap[tag.mugshot_id].push(tag.tag_type)
      })

      // STEP 6: Final merge
      console.log('🔄 [Native Service] Step 6: Merging data and creating final result')
      
      const enhancedMugshots: DatabaseMugshot[] = mugshots.map(m => ({
        ...m,
        ...ratingMap[m.id],
        wild_count: tagMap[m.id]?.wild || 0,
        funny_count: tagMap[m.id]?.funny || 0,
        spooky_count: tagMap[m.id]?.spooky || 0,
        user_rating: userRatingMap[m.id] || null,
        user_tags: userTagMap[m.id] || []
      }))

      console.log(`🎯 [Native Service] Successfully processed ${enhancedMugshots.length} mugshots`)
      return enhancedMugshots

    } catch (error) {
      console.error('❌ [Native Service] Error in getFilteredMugshots:', error)
      return []
    }
  }

  /**
   * Count total filtered mugshots - follows the same efficient filtering pattern
   * PRESERVES: Exact same method signature and behavior
   */
  async getMugshotCount(filters: MugshotFilters = {}): Promise<number> {
    try {
             console.log('🔢 [Native Service] Counting filtered mugshots with filters:', filters)
      
      const supabase = await createClient()
      let filterIds: number[] | null = null

      // Apply tag filtering first (same as main query)
      if (filters.tags && filters.tags.length > 0) {
        console.log('🏷️ [Native Service] Applying tag filtering to count query')
        
        const { data: tagged, error: tagError } = await supabase
          .from('tags')
          .select('mugshot_id')
          .in('tag_type', filters.tags)

        if (tagError) {
          console.error('❌ [Native Service] Tag filtering error in count:', tagError)
          return 0
        }

        filterIds = tagged?.map(t => t.mugshot_id) || []
        
        if (filterIds.length === 0) {
          return 0
        }
      }

      // Build count query with same filters as main query
      let query = supabase
        .from('mugshots')
        .select('*', { count: 'exact', head: true })

      // Apply ID filter if we have it
      if (filterIds?.length) {
        query = query.in('id', filterIds)
      }

      // Apply other filters (same as main query)
      if (filters.searchTerm) {
        query = query.or(`firstName.ilike.%${filters.searchTerm}%,lastName.ilike.%${filters.searchTerm}%`)
      }

      if (filters.state && filters.state !== 'all-states') {
        query = query.eq('stateOfBooking', filters.state)
      }

      if (filters.county && filters.county !== 'all-counties') {
        query = query.eq('countyOfBooking', filters.county)
      }

      if (filters.dateFrom) {
        query = query.gte('dateOfBooking', filters.dateFrom)
      }

      if (filters.dateTo) {
        query = query.lte('dateOfBooking', filters.dateTo)
      }

      const { count, error } = await query

      if (error) {
        console.error('❌ [Native Service] Count query failed:', error)
        return 0
      }

      const totalCount = count || 0
      console.log(`🔢 [Native Service] Count query result: ${totalCount}`)
      return totalCount

    } catch (error) {
      console.error('❌ [Native Service] Error in getMugshotCount:', error)
      return 0
    }
  }

  /**
   * Get single mugshot by ID with enhanced data
   * PRESERVES: Exact same method signature and behavior
   */
  async getMugshotById(id: number, userId?: string): Promise<DatabaseMugshot | null> {
    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('mugshots')
        .select(`
          id,
          created_at,
          firstName,
          lastName,
          dateOfBooking,
          stateOfBooking,
          countyOfBooking,
          offenseDescription,
          additionalDetails,
          imagePath,
          fb_status,
          adsText,
          jb_post_link,
          jb_fb_post
        `)
        .eq('id', id)
        .single()

      if (error || !data) {
        return null
      }

      // Get enhanced data for this single mugshot (same as original)
      const [ratingStats, tagStats, userRating, userTags] = await Promise.all([
        this.getBatchRatingStatistics([id]),
        this.getBatchTagStatistics([id]),
        userId ? this.getBatchUserRatings([id], userId) : Promise.resolve({} as Record<number, number>),
        userId ? this.getBatchUserTags([id], userId) : Promise.resolve({} as Record<number, string[]>)
      ])

      return {
        ...data,
        average_rating: ratingStats[id]?.average_rating || 0,
        total_ratings: ratingStats[id]?.total_ratings || 0,
        wild_count: tagStats[id]?.wild || 0,
        funny_count: tagStats[id]?.funny || 0,
        spooky_count: tagStats[id]?.spooky || 0,
        user_rating: userRating[id] || null,
        user_tags: userTags[id] || []
      }

    } catch (error) {
      console.error('❌ [Native Service] Error fetching mugshot by ID:', error)
      return null
    }
  }



  // ============================================================================
  // HELPER METHODS - EXACT COPIES from original service (CRITICAL - DO NOT CHANGE)
  // ============================================================================

  /**
   * Batch fetch rating statistics for multiple mugshots
   * PRESERVES: Exact same implementation as original service
   */
  private async getBatchRatingStatistics(mugshotIds: number[]): Promise<Record<number, { average_rating: number; total_ratings: number }>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('ratings')
        .select('mugshot_id, rating')
        .in('mugshot_id', mugshotIds)

      if (error) {
        console.error('Error fetching rating statistics:', error)
        return {}
      }

      const stats: Record<number, { ratings: number[]; average_rating: number; total_ratings: number }> = {}
      
      data?.forEach(rating => {
        if (!stats[rating.mugshot_id]) {
          stats[rating.mugshot_id] = { ratings: [], average_rating: 0, total_ratings: 0 }
        }
        stats[rating.mugshot_id].ratings.push(rating.rating)
      })

      const result: Record<number, { average_rating: number; total_ratings: number }> = {}
      Object.keys(stats).forEach(mugshotIdStr => {
        const mugshotId = parseInt(mugshotIdStr)
        const ratings = stats[mugshotId].ratings
        result[mugshotId] = {
          average_rating: ratings.length > 0 
            ? Math.round((ratings.reduce((sum, r) => sum + r, 0) / ratings.length) * 100) / 100
            : 0,
          total_ratings: ratings.length
        }
      })

      return result
    } catch (error) {
      console.error('Error in getBatchRatingStatistics:', error)
      return {}
    }
  }

  /**
   * Batch fetch tag statistics for multiple mugshots
   * PRESERVES: Exact same implementation as original service
   */
  private async getBatchTagStatistics(mugshotIds: number[]): Promise<Record<number, { wild: number; funny: number; spooky: number }>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('tags')
        .select('mugshot_id, tag_type')
        .in('mugshot_id', mugshotIds)

      if (error) {
        console.error('Error fetching tag statistics:', error)
        return {}
      }

      const stats: Record<number, { wild: number; funny: number; spooky: number }> = {}
      
      data?.forEach(tag => {
        if (!stats[tag.mugshot_id]) {
          stats[tag.mugshot_id] = { wild: 0, funny: 0, spooky: 0 }
        }
        if (tag.tag_type === 'wild') stats[tag.mugshot_id].wild++
        else if (tag.tag_type === 'funny') stats[tag.mugshot_id].funny++
        else if (tag.tag_type === 'spooky') stats[tag.mugshot_id].spooky++
      })

      return stats
    } catch (error) {
      console.error('Error in getBatchTagStatistics:', error)
      return {}
    }
  }

  /**
   * Batch fetch user ratings for multiple mugshots
   * PRESERVES: Exact same implementation as original service
   */
  private async getBatchUserRatings(mugshotIds: number[], userId: string): Promise<Record<number, number>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('ratings')
        .select('mugshot_id, rating')
        .in('mugshot_id', mugshotIds)
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user ratings:', error)
        return {}
      }

      const result: Record<number, number> = {}
      data?.forEach(rating => {
        result[rating.mugshot_id] = rating.rating
      })

      return result
    } catch (error) {
      console.error('Error in getBatchUserRatings:', error)
      return {}
    }
  }

  /**
   * Batch fetch user tags for multiple mugshots  
   * PRESERVES: Exact same implementation as original service
   */
  private async getBatchUserTags(mugshotIds: number[], userId: string): Promise<Record<number, string[]>> {
    if (mugshotIds.length === 0) return {}

    try {
      const supabase = await createClient()
      
      const { data, error } = await supabase
        .from('tags')
        .select('mugshot_id, tag_type')
        .in('mugshot_id', mugshotIds)
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching user tags:', error)
        return {}
      }

      const result: Record<number, string[]> = {}
      data?.forEach(tag => {
        if (!result[tag.mugshot_id]) {
          result[tag.mugshot_id] = []
        }
        result[tag.mugshot_id].push(tag.tag_type)
      })

      return result
    } catch (error) {
      console.error('Error in getBatchUserTags:', error)
      return {}
    }
  }
}

// Export singleton instance (same pattern as original service)
export const mugshotsNativeService = new MugshotsNativeService() 