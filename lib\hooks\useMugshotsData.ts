import { useState, useCallback } from 'react'
import { mugshotsService } from '@/lib/services/mugshots-service'
import { transformDBMugshotsToUI, UIMugshot } from '@/lib/utils/mugshot-transforms'
import type { MugshotFilters, SortOptions, PaginationOptions } from '@/lib/services/mugshots-service'

interface UseMugshotsDataState {
  mugshots: UIMugshot[]
  totalCount: number
  loading: boolean
  error: string | null
  isHealthy: boolean
}

interface UseMugshotsDataReturn extends UseMugshotsDataState {
  fetchMugshots: (
    filters?: MugshotFilters,
    sort?: SortOptions,
    pagination?: PaginationOptions
  ) => Promise<void>
  clearError: () => void
}

export function useMugshotsData(): UseMugshotsDataReturn {
  const [state, setState] = useState<UseMugshotsDataState>({
    mugshots: [],
    totalCount: 0,
    loading: false,
    error: null,
    isHealthy: true
  })

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const fetchMugshots = useCallback(async (
    filters: MugshotFilters = {},
    sort: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 }
  ) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }))

      console.log('🚀 Fetching mugshots with:', { filters, sort, pagination })

      // Fetch mugshots and total count in parallel
      const [dbMugshots, totalCount] = await Promise.all([
        mugshotsService.getMugshots(filters, sort, pagination),
        mugshotsService.getMugshotCount(filters)
      ])

      console.log('📦 Received data:', { 
        mugshotsCount: dbMugshots.length, 
        totalCount 
      })

      // Transform database data to UI format
      const uiMugshots = transformDBMugshotsToUI(dbMugshots)

      setState(prev => ({
        ...prev,
        mugshots: uiMugshots,
        totalCount,
        loading: false,
        isHealthy: true
      }))

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      console.error('❌ Error fetching mugshots:', error)
      
      setState(prev => ({
        ...prev,
        mugshots: [],
        totalCount: 0,
        loading: false,
        error: errorMessage,
        isHealthy: false
      }))
    }
  }, [])

  return {
    ...state,
    fetchMugshots,
    clearError
  }
} 