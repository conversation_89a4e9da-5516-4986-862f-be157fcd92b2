-- Create view for available states with coordinates
CREATE OR REPLACE VIEW public.available_states_with_coordinates AS
SELECT DISTINCT
  m."stateOfBooking" as state_name,
  sc.latitude,
  sc.longitude,
  COUNT(m.id) as total_mugshots
FROM public.mugshots m
INNER JOIN public.state_coordinates sc ON m."stateOfBooking" = sc.state_name
WHERE m."stateOfBooking" IS NOT NULL 
  AND m."stateOfBooking" != ''
GROUP BY m."stateOfBooking", sc.latitude, sc.longitude
ORDER BY m."stateOfBooking";
