import { createClient } from '@/lib/supabase/client'
import type { TagType } from '@/lib/constants'

// Types
export interface MugshotFilters {
  searchTerm?: string
  state?: string
  county?: string
  dateFrom?: string
  dateTo?: string
  category?: string[] // Legacy field, will be deprecated
  tags?: TagType[] // Updated to use proper TagType
}

export interface SortOptions {
  sortBy: 'newest' | 'top-rated' | 'most-viewed'
}

export interface PaginationOptions {
  page: number
  perPage: number
}

export interface DatabaseMugshot {
  id: number
  created_at: string
  firstName: string
  lastName: string
  dateOfBooking: string | null
  stateOfBooking: string | null
  countyOfBooking: string | null
  offenseDescription: string | null
  additionalDetails: string | null
  imagePath: string | null
  fb_status: string | null
  adsText: string | null
  jb_post_link: string | null
  jb_fb_post: boolean
}

class MugshotsService {
  private supabase = createClient()

  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.supabase.from('mugshots').select('id').limit(1)
      return !error
    } catch {
      return false
    }
  }

  async getMugshots(
    filters: MugshotFilters = {},
    sortOptions: SortOptions = { sortBy: 'newest' },
    pagination: PaginationOptions = { page: 1, perPage: 12 }
  ): Promise<DatabaseMugshot[]> {
         let query = this.supabase
       .from('mugshots')
       .select(`
         id,
         created_at,
         firstName,
         lastName,
         dateOfBooking,
         stateOfBooking,
         countyOfBooking,
         offenseDescription,
         additionalDetails,
         imagePath,
         fb_status,
         adsText,
         jb_post_link,
         jb_fb_post
       `)

    // Apply filters
    if (filters.searchTerm) {
      query = query.or(`firstName.ilike.%${filters.searchTerm}%,lastName.ilike.%${filters.searchTerm}%`)
    }

    if (filters.state && filters.state !== 'all-states') {
      query = query.eq('stateOfBooking', filters.state)
    }

    if (filters.county && filters.county !== 'all-counties') {
      query = query.eq('countyOfBooking', filters.county)
    }

    if (filters.dateFrom) {
      query = query.gte('dateOfBooking', filters.dateFrom)
    }

    if (filters.dateTo) {
      query = query.lte('dateOfBooking', filters.dateTo)
    }

         // Skip category filter for now since we don't have that field yet
     // if (filters.category && filters.category.length > 0) {
     //   query = query.in('jb_category', filters.category)
     // }

     // Apply sorting - for now only 'newest' works with real data
     switch (sortOptions.sortBy) {
       case 'top-rated':
       case 'most-viewed':
       case 'newest':
       default:
         query = query.order('created_at', { ascending: false })
         break
     }

    // Apply pagination
    const from = (pagination.page - 1) * pagination.perPage
    const to = from + pagination.perPage - 1
    query = query.range(from, to)

    const { data, error } = await query

    if (error) {
      console.error('Error fetching mugshots:', error)
      throw new Error('Failed to fetch mugshots')
    }

    return data || []
  }

  async getMugshotCount(filters: MugshotFilters = {}): Promise<number> {
    let query = this.supabase
      .from('mugshots')
      .select('*', { count: 'exact', head: true })

    // Apply same filters as getMugshots
    if (filters.searchTerm) {
      query = query.or(`firstName.ilike.%${filters.searchTerm}%,lastName.ilike.%${filters.searchTerm}%`)
    }

    if (filters.state && filters.state !== 'all-states') {
      query = query.eq('stateOfBooking', filters.state)
    }

    if (filters.county && filters.county !== 'all-counties') {
      query = query.eq('countyOfBooking', filters.county)
    }

    if (filters.dateFrom) {
      query = query.gte('dateOfBooking', filters.dateFrom)
    }

    if (filters.dateTo) {
      query = query.lte('dateOfBooking', filters.dateTo)
    }

         // Skip category filter for now since we don't have that field yet
     // if (filters.category && filters.category.length > 0) {
     //   query = query.in('jb_category', filters.category)
     // }

    const { count, error } = await query

    if (error) {
      console.error('Error fetching mugshot count:', error)
      throw new Error('Failed to fetch mugshot count')
    }

    return count || 0
  }
}

// Export a single instance for client-side use
export const mugshotsService = new MugshotsService() 