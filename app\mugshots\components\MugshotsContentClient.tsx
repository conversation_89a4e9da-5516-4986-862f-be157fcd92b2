"use client"

import { useFilterS<PERSON> } from "@/lib/stores/filter-store"
import MugshotsGrid from "./MugshotsGrid"
import MugshotsGridSkeleton from "@/components/MugshotsGridSkeleton"
import MugshotsPagination from "./MugshotsPagination"
import { Search } from "lucide-react"
import type { UIMugshot } from "@/lib/utils/mugshot-transforms"

interface MugshotsContentClientProps {
  mugshots: UIMugshot[]
  totalCount: number
  currentPage: number
  totalPages: number
  perPage: number
  gridView: 'large' | 'compact'
  isEmpty: boolean
  isDataStale: boolean  // NEW: Indicates when current data is outdated and should be hidden
}

export default function MugshotsContentClient({
  mugshots,
  totalCount,
  currentPage,
  totalPages,
  perPage,
  gridView,
  isEmpty,
  isDataStale
}: MugshotsContentClientProps) {
  const { isLoading } = useFilterStore()

  // NEW: Determine if we should show skeleton loading
  // Show skeleton if:
  // 1. Filter store indicates loading (for backward compatibility), OR
  // 2. Data is stale (current data doesn't match current filters)
  const shouldShowSkeleton = isLoading || isDataStale

  if (shouldShowSkeleton) {
    return (
      <>
        {/* Show skeleton grid during loading or when data is stale */}
        <MugshotsGridSkeleton 
          gridView={gridView} 
          count={perPage}
        />
        
        {/* Show pagination skeleton */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <div className="flex items-center space-x-2">
              {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => (
                <div 
                  key={i}
                  className="w-10 h-10 bg-gray-700 rounded animate-pulse"
                />
              ))}
            </div>
          </div>
        )}
      </>
    )
  }

  if (isEmpty) {
    return (
      <div className="text-center py-12">
        <Search className="h-16 w-16 text-gray-600 mx-auto mb-4" />
        <h3 className="text-xl font-bold text-gray-400 mb-2">No Mugshots Found</h3>
        <p className="text-gray-500">Try adjusting your filters to see more results</p>
      </div>
    )
  }

  return (
    <>
      {/* Client Component for Grid - Receives server data as props */}
      <MugshotsGrid 
        mugshots={mugshots}
        gridView={gridView}
      />

      {/* Server Component for Pagination */}
      {totalPages > 1 && (
        <MugshotsPagination 
          currentPage={currentPage}
          totalPages={totalPages}
          totalCount={totalCount}
        />
      )}
    </>
  )
} 