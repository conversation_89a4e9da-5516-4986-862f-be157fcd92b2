import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Longer stale times to reduce unnecessary refetches
      staleTime: 2 * 60 * 1000, // 2 minutes default
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      
      // Robust retry configuration
      retry: (failureCount, error) => {
        // Don't retry authentication errors
        if (error?.message?.includes('UNAUTHENTICATED') || error?.message?.includes('login')) {
          return false
        }
        
        // Don't retry client errors (4xx) except for 429 (too many requests)
        if (error?.message?.includes('not found') || 
            error?.message?.includes('invalid') || 
            error?.message?.includes('format')) {
          return false
        }
        
        // Retry network errors and server errors up to 3 times
        return failureCount < 3
      },
      
      // Exponential backoff for retries
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      
      // Don't refetch on window focus to prevent unnecessary requests
      refetchOnWindowFocus: false,
      
      // Refetch on reconnect for fresh data
      refetchOnReconnect: true,
      
      // Don't refetch on mount unless data is stale
      refetchOnMount: (query) => query.isStale(),
      
      // Enable background refetching for better UX
      refetchInterval: false, // We'll handle this per-query if needed
      
      // Global error handling to prevent unhandled promise rejections
      throwOnError: false,
      
      // Error handling is done via throwOnError: false above and individual query error handlers
    },
    mutations: {
      // Retry failed mutations once for network errors
      retry: (failureCount, error) => {
        // Don't retry authentication or validation errors
        if (error?.message?.includes('UNAUTHENTICATED') || 
            error?.message?.includes('invalid') ||
            error?.message?.includes('not found')) {
          return false
        }
        
        // Retry network and server errors once
        return failureCount < 1
      },
      
      // Faster retry for mutations
      retryDelay: 1000,
      
      // Error handling for mutations is handled individually in each mutation
    },
  },
  
  // Query client configured for production use
}) 