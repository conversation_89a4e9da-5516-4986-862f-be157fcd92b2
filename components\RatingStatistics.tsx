'use client'

import { useState, useEffect } from 'react'
import { Star, Users, Heart, Activity } from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'

import { useRatingStatisticsQuery } from '@/lib/hooks/queries/use-rating-statistics-query'
import { useRatingUpdates } from '@/lib/hooks/useRatingUpdates'
import type { RatingStatistics as Stats } from '@/lib/types/database'

interface RatingStatisticsProps {
  mugshotId: string
  compact?: boolean
  showDetails?: boolean
  showDistribution?: boolean
  realTimeUpdates?: boolean
  className?: string
  preloadedStats?: Stats // For optimization - skips API call if provided
}

export default function RatingStatistics({ 
  mugshotId, 
  compact = false, 
  showDetails = true,
  showDistribution = false,
  realTimeUpdates = true,
  className = '',
  preloadedStats
}: RatingStatisticsProps) {
  // TanStack Query hook for rating statistics
  const { 
    data: stats, 
    isLoading, 
    error: queryError 
  } = useRatingStatisticsQuery(mugshotId, true, preloadedStats)
  
  const [error, setError] = useState<string | null>(null)

  // Real-time updates hook
  const { isConnected } = useRatingUpdates(mugshotId, realTimeUpdates)

  // Handle query errors
  useEffect(() => {
    if (queryError) {
      // Ensure we always display a user-friendly error message
      let errorMessage = 'Failed to load rating statistics'
      
      // Extract meaningful error message without exposing technical details
      if (queryError instanceof Error) {
        const message = queryError.message.toLowerCase()
        if (message.includes('network') || message.includes('fetch')) {
          errorMessage = 'Network error - please check your connection'
        } else if (message.includes('timeout')) {
          errorMessage = 'Request timed out - please try again'
        } else if (message.includes('not found') || message.includes('404')) {
          errorMessage = 'Rating data not found'
        } else if (message.includes('server') || message.includes('500')) {
          errorMessage = 'Server error - please try again later'
        } else {
          errorMessage = 'Unable to load ratings - please refresh the page'
        }
      }
      
      setError(errorMessage)
      console.error('Rating statistics error:', queryError)
    } else {
      setError(null)
    }
  }, [queryError])

  // Get star rating display (convert 1-10 to 5 stars)
  const getStarRating = (rating: number) => {
    const starRating = (rating / 10) * 5 // Convert 10-point to 5-star
    const fullStars = Math.floor(starRating)
    const hasHalfStar = starRating % 1 >= 0.5
    
    return { fullStars, hasHalfStar, starRating }
  }

  // Get quality assessment based on rating
  const getQualityInfo = () => {
    if (!stats || stats.totalRatings < 3) {
      return { badge: null, color: 'text-muted-foreground', description: 'Not enough ratings' }
    }
    
    const { averageRating } = stats
    
    if (averageRating >= 8) {
      return { 
        badge: <Badge className="bg-green-600 text-white">🔥 Excellent</Badge>,
        color: 'text-green-600',
        description: 'Highly rated by the community'
      }
    } else if (averageRating >= 6) {
      return { 
        badge: <Badge variant="default" className="bg-blue-600 text-white">⭐ Good</Badge>,
        color: 'text-blue-600',
        description: 'Well received by users'
      }
    } else if (averageRating >= 4) {
      return { 
        badge: <Badge variant="secondary">📊 Average</Badge>,
        color: 'text-yellow-600',
        description: 'Mixed reviews from users'
      }
    } else {
      return { 
        badge: <Badge variant="destructive">👎 Poor</Badge>,
        color: 'text-red-600',
        description: 'Below community average'
      }
    }
  }

  // Render star display
  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const { fullStars, hasHalfStar } = getStarRating(rating)
    const sizeClass = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-6 w-6' : 'h-4 w-4'
    
    return (
      <div className="flex items-center">
        {Array.from({ length: 5 }, (_, i) => {
          if (i < fullStars) {
            return <Star key={i} className={`${sizeClass} fill-yellow-400 text-yellow-400`} />
          } else if (i === fullStars && hasHalfStar) {
            return (
              <div key={i} className="relative">
                <Star className={`${sizeClass} text-gray-300`} />
                <Star className={`${sizeClass} fill-yellow-400 text-yellow-400 absolute top-0 left-0`} style={{ clipPath: 'inset(0 50% 0 0)' }} />
              </div>
            )
          } else {
            return <Star key={i} className={`${sizeClass} text-gray-300`} />
          }
        })}
      </div>
    )
  }

  // Distribution feature removed - not available in simplified interface
  const renderDistribution = () => {
    // Distribution not available with current API structure
    return null
  }

  // Render loading state
  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-4 w-4 rounded" />
            <Skeleton className="h-4 w-16" />
          </div>
          {!compact && (
            <Skeleton className="h-3 w-24" />
          )}
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div className={`text-red-500 text-sm ${className}`}>
        {error}
      </div>
    )
  }

  // Render no ratings state
  if (!stats || stats.totalRatings === 0) {
    return (
      <div className={`text-muted-foreground text-sm ${className}`}>
        {compact ? 'No ratings' : 'Be the first to rate!'}
      </div>
    )
  }

  const qualityInfo = getQualityInfo()

  // Render compact view (for cards)
  if (compact) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="flex items-center space-x-1">
          {renderStars(stats.averageRating, 'sm')}
          <span className="font-medium text-sm ml-1">
            {stats.averageRating.toFixed(1)}
          </span>
        </div>
        <span className="text-muted-foreground text-xs">
          ({stats.totalRatings})
        </span>
        {qualityInfo.badge}
                 {realTimeUpdates && isConnected && (
           <Activity className="h-3 w-3 text-green-500" />
         )}
      </div>
    )
  }

  // Render detailed view (for detail pages)
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-lg">
            <Heart className="h-5 w-5 text-red-500" />
            Attractiveness Rating
          </div>
          {realTimeUpdates && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Activity className={`h-3 w-3 ${isConnected ? 'text-green-500' : 'text-gray-400'}`} />
              {isConnected ? 'Live' : 'Offline'}
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Rating */}
        <div className="text-center space-y-3">
          <div className="space-y-2">
            <div className="flex items-center justify-center">
              {renderStars(stats.averageRating, 'lg')}
            </div>
            <div className="flex items-center justify-center space-x-2">
              <span className="text-3xl font-bold">
                {stats.averageRating.toFixed(1)}
              </span>
              <span className="text-muted-foreground">/ 10</span>
            </div>
          </div>
          <p className="text-muted-foreground">
            Based on {stats.totalRatings} {stats.totalRatings === 1 ? 'rating' : 'ratings'}
          </p>
          <div className="flex justify-center">
            {qualityInfo.badge}
          </div>
          <p className="text-sm text-muted-foreground">
            {qualityInfo.description}
          </p>
        </div>

        {/* Additional Statistics */}
        {showDetails && stats.totalRatings >= 3 && (
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="text-center space-y-1">
              <div className="flex items-center justify-center gap-1">
                <Users className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-muted-foreground">Total Votes</span>
              </div>
              <p className="text-xl font-semibold">{stats.totalRatings}</p>
            </div>
            {/* Percentile feature removed - not available in simplified interface */}
          </div>
        )}

        {/* Rating Distribution */}
        {showDistribution && renderDistribution()}

        {/* Engagement Indicator */}
        {stats.totalRatings >= 20 && (
          <div className="text-center p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border">
            <p className="text-sm font-medium text-purple-700">
              🔥 Trending! This mugshot has received {stats.totalRatings}+ ratings
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 